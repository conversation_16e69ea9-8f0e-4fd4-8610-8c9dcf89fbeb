I want to replace the current page switching logic in `src/pages/Index.tsx` with proper React Router routing. Instead of controlling page navigation through component state, I want to create URL-based routes for better navigation and bookmarking.

Please implement the following routes:
1. `/orders` - Should render the OrderPage component (this should be the homepage/default route)
2. `/order-manage` - Should render the OrderManagementPage component  
3. `/statistics` - Should render the DashboardPage component (rename this route from 'dashboard' to 'statistics')

Requirements:
- Set up React Router with proper route definitions
- Make `/orders` the default route that loads when users visit the root URL (`/`)
- Update any existing navigation components to use proper Link components instead of state-based page switching
- Ensure the routing works with browser back/forward buttons
- Remove the current `currentPage` state-based navigation system