Refactor the OrderConfirmationDialog component (`src/components/OrderConfirmationDialog.tsx`) to implement persistent customer name storage and auto-fill functionality:

**Requirements:**

1. **Create a custom React hook** (`useCustomerName` or similar) that:
   - Stores the customer name in localStorage for permanent persistence (survives browser restarts)
   - Provides methods to get and set the customer name
   - Handles localStorage errors gracefully (e.g., when localStorage is disabled)

2. **Implement auto-fill functionality:**
   - When the OrderConfirmationDialog opens, automatically populate the customer name input field with the previously saved name from localStorage
   - Only auto-fill if a name was previously saved (don't show "null" or "undefined")

3. **Update storage on successful order:**
   - Save the customer name to localStorage when an order is successfully confirmed
   - This should happen after the order is saved to the database, not just when the user types

4. **Maintain existing functionality:**
   - Keep all current validation (required field check)
   - Preserve the existing loading states and error handling
   - Ensure the form still resets properly after successful order submission

**Technical considerations:**
- Use a localStorage key like `food-order-customer-name` for the stored value
- Handle edge cases where localStorage might not be available
- Consider using useEffect to load the saved name when the dialog opens
- Ensure the hook is reusable and follows React best practices

**Expected behavior:**
- First-time users see an empty input field
- Returning users see their previously entered name pre-filled
- The name persists across browser sessions and page refreshes
- Users can still modify the pre-filled name if needed