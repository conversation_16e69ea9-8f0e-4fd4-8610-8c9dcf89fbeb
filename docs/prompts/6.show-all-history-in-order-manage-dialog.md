Modify the OrderHistoryDialog component to display comprehensive order history data instead of just the current user's history:

**Data Requirements:**
- Fetch all order history records from the `order_history` database table for all users (not just the current user)
- Filter the data to show only orders from today's date
- Sort the results chronologically with the oldest orders displayed at the top and the newest orders at the bottom

**Implementation Details:**
- Update the data fetching logic to retrieve all users' order history
- Apply date filtering to show only today's orders (based on order creation date/timestamp)
- Ensure proper sorting by timestamp/date in ascending order (oldest first)
- Maintain the existing dialog UI structure while displaying the expanded dataset
- Consider performance implications when fetching all users' data and implement appropriate optimization if needed

**Expected Outcome:**
The OrderHistoryDialog should now show a comprehensive view of all order activity for the current day across all users, sorted chronologically from oldest to newest.