I want to create a products database table and populate it with the menu data listed below, then modify the OrderPage.tsx component to display and use this product data.

**Database Requirements:**
1. Create a `products` table with the following schema:
   - id (primary key)
   - name (product name)
   - price (numeric value in yen)
   - category (either "side_dish" or "main_dish")
   - created_at (timestamp)
   - updated_at (timestamp)
   - deleted_at (nullable timestamp for soft deletes)

2. Populate the table with this menu data:

**Side Dishes (Món phụ) - category: "side_dish":**
- キャベツ - ¥30
- ポテト　サラダ - ¥100
- みそ汁 - ¥100
- ごはん（小） - ¥50
- 唐揚げ　パック - ¥100

**Main Dishes (Món chính) - category: "main_dish":**
- 唐揚げ - ¥400
- のり (Thập Cẩm - Nhỏ) - ¥400
- そぼろ - ¥400
- ハンバーグ - ¥440
- メンチカツ - ¥440
- 生姜焼き - ¥440
- とんかつ - ¥440
- 焼き肉 - ¥440
- 焼き魚 - ¥440
- カキフライ - ¥440
- スポーツ (<PERSON>h<PERSON><PERSON>ẩm - Lớn) - ¥500

**Frontend Requirements:**
3. Update `src/pages/OrderPage.tsx` to:
   - Fetch product data from the database
   - Display products organized by category (side dishes and main dishes)
   - Show product names and prices
   - Allow users to interact with the product data (likely for ordering purposes)

Please implement the database schema, insert the product data, and modify the OrderPage component to display this information in a user-friendly format.