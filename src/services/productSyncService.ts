import { supabaseOrderService } from "./supabaseOrderService";
import { allFoodItems } from "@/data/foodItems";

export interface ProductSyncResult {
  success: boolean;
  syncedCount?: number;
  error?: string;
}

export const productSyncService = {
  /**
   * Synchronizes all local food items with the Supabase products table
   * This should be called on application startup to ensure all products exist
   */
  async syncAllProducts(): Promise<ProductSyncResult> {
    try {
      console.log("Starting product synchronization...");
      
      const result = await supabaseOrderService.ensureProductsExist(allFoodItems);
      
      if (result.success && result.productMap) {
        const syncedCount = result.productMap.size;
        console.log(`Product synchronization completed. ${syncedCount} products available.`);
        
        return {
          success: true,
          syncedCount,
        };
      } else {
        console.error("Product synchronization failed:", result.error);
        return {
          success: false,
          error: result.error || "Unknown error during product synchronization",
        };
      }
    } catch (error) {
      console.error("Unexpected error during product synchronization:", error);
      return {
        success: false,
        error: `Unexpected error: ${error}`,
      };
    }
  },

  /**
   * Checks if products are synchronized by verifying a few key products exist
   */
  async checkProductSync(): Promise<{ isSynced: boolean; error?: string }> {
    try {
      // Check a sample of products to see if they're synced
      const sampleProducts = allFoodItems.slice(0, 3);
      const result = await supabaseOrderService.ensureProductsExist(sampleProducts);
      
      return {
        isSynced: result.success && result.productMap?.size === sampleProducts.length,
        error: result.error,
      };
    } catch (error) {
      return {
        isSynced: false,
        error: `Error checking product sync: ${error}`,
      };
    }
  },
};
