import { supabase } from "@/integrations/supabase/client";
import { FoodItem } from "@/types/food";

export interface ProductServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface DatabaseProduct {
  id: string;
  name: string;
  price: number;
  category: "main" | "side";
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
}

/**
 * Service for managing products from the database
 */
export const productService = {
  /**
   * Fetches all active products from the database
   */
  async getAllProducts(): Promise<ProductServiceResult<FoodItem[]>> {
    try {
      console.log("🔍 Fetching products from database...");

      const { data: products, error } = await supabase
        .from("products")
        .select("id, name, price, category, created_at, updated_at, deleted_at")
        .is("deleted_at", null)
        .order("category", { ascending: true })
        .order("name", { ascending: true });

      if (error) {
        console.error("❌ Error fetching products:", error);
        return {
          success: false,
          error: `Failed to fetch products: ${error.message}`,
        };
      }

      if (!products) {
        console.log("⚠️ No products found in database");
        return {
          success: true,
          data: [],
        };
      }

      // Convert database products to FoodItem format
      const foodItems: FoodItem[] = products.map((product: DatabaseProduct, index: number) => ({
        id: `${product.category}-${index + 1}`, // Generate consistent IDs for frontend
        name: product.name,
        price: product.price,
        type: product.category,
      }));

      console.log(`✅ Successfully fetched ${foodItems.length} products from database`);
      return {
        success: true,
        data: foodItems,
      };
    } catch (error) {
      console.error("❌ Unexpected error fetching products:", error);
      return {
        success: false,
        error: `Unexpected error: ${error}`,
      };
    }
  },

  /**
   * Fetches products by category
   */
  async getProductsByCategory(category: "main" | "side"): Promise<ProductServiceResult<FoodItem[]>> {
    try {
      console.log(`🔍 Fetching ${category} products from database...`);

      const { data: products, error } = await supabase
        .from("products")
        .select("id, name, price, category, created_at, updated_at, deleted_at")
        .eq("category", category)
        .is("deleted_at", null)
        .order("name", { ascending: true });

      if (error) {
        console.error(`❌ Error fetching ${category} products:`, error);
        return {
          success: false,
          error: `Failed to fetch ${category} products: ${error.message}`,
        };
      }

      if (!products) {
        console.log(`⚠️ No ${category} products found in database`);
        return {
          success: true,
          data: [],
        };
      }

      // Convert database products to FoodItem format
      const foodItems: FoodItem[] = products.map((product: DatabaseProduct, index: number) => ({
        id: `${product.category}-${index + 1}`, // Generate consistent IDs for frontend
        name: product.name,
        price: product.price,
        type: product.category,
      }));

      console.log(`✅ Successfully fetched ${foodItems.length} ${category} products from database`);
      return {
        success: true,
        data: foodItems,
      };
    } catch (error) {
      console.error(`❌ Unexpected error fetching ${category} products:`, error);
      return {
        success: false,
        error: `Unexpected error: ${error}`,
      };
    }
  },

  /**
   * Checks if products are available in the database
   */
  async checkProductsAvailability(): Promise<ProductServiceResult<{ count: number }>> {
    try {
      console.log("🔍 Checking products availability...");

      const { data, error, count } = await supabase
        .from("products")
        .select("id", { count: "exact", head: true })
        .is("deleted_at", null);

      if (error) {
        console.error("❌ Error checking products availability:", error);
        return {
          success: false,
          error: `Failed to check products availability: ${error.message}`,
        };
      }

      const productCount = count || 0;
      console.log(`✅ Found ${productCount} products in database`);

      return {
        success: true,
        data: { count: productCount },
      };
    } catch (error) {
      console.error("❌ Unexpected error checking products availability:", error);
      return {
        success: false,
        error: `Unexpected error: ${error}`,
      };
    }
  },
};
