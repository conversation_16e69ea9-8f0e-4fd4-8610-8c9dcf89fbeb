import { supabase } from "@/integrations/supabase/client";

/**
 * Quick test to check database tables and data
 */
export const quickTest = async () => {
  console.log("🧪 Running quick database test...");

  try {
    // Test 1: Check users table
    const { data: users, error: usersError } = await supabase
      .from("users")
      .select("*")
      .limit(5);

    if (usersError) {
      console.error("❌ Users query failed:", usersError);
    } else {
      console.log("👥 Users found:", users?.length || 0, users);
    }

    // Test 2: Check products table
    const { data: products, error: productsError } = await supabase
      .from("products")
      .select("*")
      .limit(5);

    if (productsError) {
      console.error("❌ Products query failed:", productsError);
    } else {
      console.log("🛍️ Products found:", products?.length || 0, products);
    }

    // Test 3: Check orders table
    const { data: orders, error: ordersError } = await supabase
      .from("orders")
      .select("*")
      .limit(5);

    if (ordersError) {
      console.error("❌ Orders query failed:", ordersError);
    } else {
      console.log("📋 Orders found:", orders?.length || 0, orders);
    }

    // Test 4: Check order_items table
    const { data: orderItems, error: orderItemsError } = await supabase
      .from("order_items")
      .select("*")
      .limit(5);

    if (orderItemsError) {
      console.error("❌ Order items query failed:", orderItemsError);
    } else {
      console.log("🍽️ Order items found:", orderItems?.length || 0, orderItems);
    }

    console.log("✅ Quick test completed");
    return { success: true };
  } catch (error) {
    console.error("❌ Quick test failed:", error);
    return { success: false, error: String(error) };
  }
};

// Make it available globally
declare global {
  interface Window {
    quickTest: typeof quickTest;
  }
}

window.quickTest = quickTest;

// Auto-run the test
setTimeout(() => {
  quickTest();
}, 2000);
