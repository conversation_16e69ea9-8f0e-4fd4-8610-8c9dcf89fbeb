import { supabaseOrderService } from "@/services/supabaseOrderService";
import { allFoodItems } from "@/data/foodItems";

/**
 * Utility to add sample orders for testing the OrderManagementPage
 */
export const addSampleOrders = async () => {
  console.log("🧪 Adding sample orders for testing...");

  const sampleOrders = [
    {
      customerName: "Hưng",
      items: [
        { foodItem: allFoodItems.find(item => item.name === "唐揚げ")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "みそ汁")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "ごはん（小）")!, quantity: 1 },
      ],
    },
    {
      customerName: "Ngọc",
      items: [
        { foodItem: allFoodItems.find(item => item.name === "ハンバーグ")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "ポテトサラダ")!, quantity: 1 },
      ],
    },
    {
      customerName: "Hoàng",
      items: [
        { foodItem: allFoodItems.find(item => item.name === "焼き肉")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "キャベツ")!, quantity: 1 },
      ],
    },
    {
      customerName: "Mai",
      items: [
        { foodItem: allFoodItems.find(item => item.name === "とんかつ")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "サラダ")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "みそ汁")!, quantity: 1 },
      ],
    },
    {
      customerName: "Tuấn",
      items: [
        { foodItem: allFoodItems.find(item => item.name === "スポーツ")!, quantity: 1 },
        { foodItem: allFoodItems.find(item => item.name === "だし巻き卵")!, quantity: 1 },
      ],
    },
  ];

  try {
    for (const order of sampleOrders) {
      // Filter out any undefined items
      const validItems = order.items.filter(item => item.foodItem);
      
      if (validItems.length === 0) {
        console.warn(`⚠️ No valid items found for ${order.customerName}, skipping...`);
        continue;
      }

      const totalAmount = validItems.reduce((sum, item) => sum + (item.foodItem.price * item.quantity), 0);
      
      const result = await supabaseOrderService.saveOrderToDatabase(
        order.customerName,
        validItems,
        totalAmount
      );

      if (result.success) {
        console.log(`✅ Added order for ${order.customerName}: ¥${totalAmount}`);
      } else {
        console.error(`❌ Failed to add order for ${order.customerName}:`, result.error);
      }
    }

    console.log("🎉 Sample orders added successfully!");
    return { success: true };
  } catch (error) {
    console.error("❌ Error adding sample orders:", error);
    return { success: false, error: String(error) };
  }
};

/**
 * Function to call from browser console for testing
 */
declare global {
  interface Window {
    addSampleOrders: typeof addSampleOrders;
  }
}

window.addSampleOrders = addSampleOrders;
