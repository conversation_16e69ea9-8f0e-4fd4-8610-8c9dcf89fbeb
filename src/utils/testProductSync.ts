import { productSyncService } from "@/services/productSyncService";
import { productService } from "@/services/productService";

/**
 * Test script to verify product synchronization and database operations
 */
export const testProductSync = {
  /**
   * Test the complete product sync flow
   */
  async runFullTest() {
    console.log("🧪 Starting product sync test...");
    
    try {
      // Step 1: Sync products from local data to database
      console.log("📤 Step 1: Syncing products to database...");
      const syncResult = await productSyncService.syncAllProducts();
      
      if (!syncResult.success) {
        console.error("❌ Product sync failed:", syncResult.error);
        return { success: false, error: syncResult.error };
      }
      
      console.log(`✅ Product sync successful! ${syncResult.syncedCount} products synced.`);
      
      // Step 2: Fetch products from database
      console.log("📥 Step 2: Fetching products from database...");
      const fetchResult = await productService.getAllProducts();
      
      if (!fetchResult.success) {
        console.error("❌ Product fetch failed:", fetchResult.error);
        return { success: false, error: fetchResult.error };
      }
      
      const products = fetchResult.data || [];
      console.log(`✅ Product fetch successful! ${products.length} products retrieved.`);
      
      // Step 3: Verify product categories
      console.log("🔍 Step 3: Verifying product categories...");
      const sideItems = products.filter(p => p.type === "side");
      const mainItems = products.filter(p => p.type === "main");
      
      console.log(`📦 Side items: ${sideItems.length}`);
      sideItems.forEach(item => console.log(`  - ${item.name}: ¥${item.price}`));
      
      console.log(`🍱 Main items: ${mainItems.length}`);
      mainItems.forEach(item => console.log(`  - ${item.name}: ¥${item.price}`));
      
      // Step 4: Verify expected products exist
      console.log("✅ Step 4: Verifying expected products...");
      const expectedSideItems = [
        "キャベツ",
        "ポテト　サラダ", 
        "みそ汁",
        "ごはん（小）",
        "唐揚げ　パック"
      ];
      
      const expectedMainItems = [
        "唐揚げ",
        "のり (Thập Cẩm - Nhỏ)",
        "そぼろ",
        "ハンバーグ",
        "メンチカツ",
        "生姜焼き",
        "とんかつ",
        "焼き肉",
        "焼き魚",
        "カキフライ",
        "スポーツ (Thập Cẩm - Lớn)"
      ];
      
      const missingSideItems = expectedSideItems.filter(name => 
        !sideItems.some(item => item.name === name)
      );
      
      const missingMainItems = expectedMainItems.filter(name => 
        !mainItems.some(item => item.name === name)
      );
      
      if (missingSideItems.length > 0) {
        console.warn("⚠️ Missing side items:", missingSideItems);
      }
      
      if (missingMainItems.length > 0) {
        console.warn("⚠️ Missing main items:", missingMainItems);
      }
      
      if (missingSideItems.length === 0 && missingMainItems.length === 0) {
        console.log("✅ All expected products found!");
      }
      
      return {
        success: true,
        syncedCount: syncResult.syncedCount,
        fetchedCount: products.length,
        sideItemsCount: sideItems.length,
        mainItemsCount: mainItems.length,
        missingSideItems,
        missingMainItems
      };
      
    } catch (error) {
      console.error("❌ Test failed with error:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Quick test to check if products are available
   */
  async quickCheck() {
    console.log("🔍 Quick product availability check...");
    
    try {
      const result = await productService.checkProductsAvailability();
      
      if (result.success && result.data) {
        console.log(`✅ ${result.data.count} products available in database`);
        return { success: true, count: result.data.count };
      } else {
        console.error("❌ Product availability check failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Quick check failed:", error);
      return { success: false, error: String(error) };
    }
  }
};

// Make it available globally for testing in browser console
declare global {
  interface Window {
    testProductSync: typeof testProductSync;
  }
}

window.testProductSync = testProductSync;
