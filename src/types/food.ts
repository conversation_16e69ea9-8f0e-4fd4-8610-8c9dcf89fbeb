export interface FoodItem {
  id: string;
  name: string;
  price: number;
  type: 'side' | 'main';
}

export interface OrderItem {
  foodItem: FoodItem;
  quantity: number;
}

export interface Order {
  id: string;
  customerName: string;
  items: OrderItem[];
  totalAmount: number;
  createdAt: Date;
  isPaid: boolean;
}

export interface DailyStats {
  totalRevenue: number;
  totalOrders: number;
  totalDebt: number;
  paidOrders: number;
  unpaidOrders: number;
  topItems: Array<{
    item: FoodItem;
    quantity: number;
  }>;
}