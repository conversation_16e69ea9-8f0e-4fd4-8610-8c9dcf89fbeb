import { useMemo } from "react";
import { StatCard } from "@/components/StatCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { orderService } from "@/services/orderService";
import { 
  DollarSign, 
  Users, 
  CreditCard, 
  TrendingUp,
  TrendingDown,
  Trophy,
  Calendar
} from "lucide-react";
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";

export function DashboardPage() {
  const stats = useMemo(() => orderService.getDailyStats(), []);
  
  const formatPrice = (price: number) => `¥${price.toLocaleString()}`;
  
  const todayDate = new Date().toLocaleDateString('vi-VN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Hard-coded data for pie chart (main dishes ratio)
  const mainDishesData = [
    { name: '唐揚げ', value: 25, color: '#8884d8' },
    { name: 'ハンバーグ', value: 20, color: '#82ca9d' },
    { name: '焼き肉', value: 15, color: '#ffc658' },
    { name: 'とんかつ', value: 12, color: '#ff7300' },
    { name: 'スポーツ', value: 10, color: '#00ff88' },
    { name: 'Khác', value: 18, color: '#8dd1e1' },
  ];

  // Hard-coded data for bar chart (weekly orders)
  const weeklyOrdersData = [
    { day: 'T2', orders: 12 },
    { day: 'T3', orders: 19 },
    { day: 'T4', orders: 8 },
    { day: 'T5', orders: 15 },
    { day: 'T6', orders: 23 },
    { day: 'T7', orders: 7 },
    { day: 'CN', orders: 5 },
  ];

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            📊 Dashboard Thống Kê
          </h1>
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{todayDate}</span>
          </div>
        </div>

        {/* Main Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="💰 Doanh Thu Hôm Nay"
            value={formatPrice(stats.totalRevenue)}
            description="Tổng tiền các đơn hàng"
            icon={DollarSign}
            gradient="bg-gradient-to-br from-green-50 to-emerald-100"
          />
          
          <StatCard
            title="👥 Số Người Đặt Cơm"
            value={stats.totalOrders}
            description="Đơn hàng hôm nay"
            icon={Users}
            gradient="bg-gradient-to-br from-blue-50 to-indigo-100"
          />
          
          <StatCard
            title="💸 Tổng Nợ Hiện Tại"
            value={formatPrice(stats.totalDebt)}
            description="Chưa thanh toán"
            icon={CreditCard}
            gradient="bg-gradient-to-br from-red-50 to-pink-100"
          />
          
          <StatCard
            title="✅ Tỉ Lệ Thanh Toán"
            value={`${stats.totalOrders > 0 ? Math.round((stats.paidOrders / stats.totalOrders) * 100) : 0}%`}
            description={`${stats.paidOrders}/${stats.totalOrders} đã trả`}
            icon={stats.paidOrders > stats.unpaidOrders ? TrendingUp : TrendingDown}
            gradient="bg-gradient-to-br from-purple-50 to-violet-100"
          />
        </div>

        {/* Payment Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-gradient-to-br from-green-50 to-emerald-100 shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700">
                <div className="bg-green-200 p-2 rounded-lg">
                  <TrendingUp className="h-5 w-5" />
                </div>
                Đã Thanh Toán
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-800 mb-2">
                {stats.paidOrders}
              </div>
              <p className="text-green-600 text-sm">
                người đã trả tiền hôm nay
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-red-100 shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <div className="bg-red-200 p-2 rounded-lg">
                  <TrendingDown className="h-5 w-5" />
                </div>
                Chưa Thanh Toán
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-800 mb-2">
                {stats.unpaidOrders}
              </div>
              <p className="text-red-600 text-sm">
                người chưa trả tiền
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pie Chart */}
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-blue-200 p-2 rounded-lg">
                  <Trophy className="h-5 w-5 text-blue-700" />
                </div>
                🥘 Tỷ Lệ Món Chính Được Đặt
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mainDishesData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {mainDishesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Bar Chart */}
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-green-200 p-2 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-700" />
                </div>
                📊 Số Đơn Đặt Theo Ngày
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={weeklyOrdersData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="orders" fill="hsl(var(--primary))" name="Số đơn" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Items */}
        {stats.topItems.length > 0 && (
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-yellow-200 p-2 rounded-lg">
                  <Trophy className="h-5 w-5 text-yellow-700" />
                </div>
                🏆 Top 5 Món Ăn Được Đặt Nhiều Nhất Hôm Nay
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.topItems.map((item, index) => (
                  <div key={item.item.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant="secondary" 
                        className={`
                          ${index === 0 ? 'bg-yellow-200 text-yellow-800' : ''}
                          ${index === 1 ? 'bg-gray-200 text-gray-800' : ''}
                          ${index === 2 ? 'bg-orange-200 text-orange-800' : ''}
                        `}
                      >
                        #{index + 1}
                      </Badge>
                      <span className="font-medium">{item.item.name}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-muted-foreground">
                        {item.quantity} lần
                      </span>
                      <span className="font-semibold text-primary">
                        {formatPrice(item.item.price)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {stats.totalOrders === 0 && (
          <Card className="bg-gradient-card shadow-card">
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">🍱</div>
              <h3 className="text-xl font-semibold text-muted-foreground mb-2">
                Chưa có đơn hàng nào hôm nay
              </h3>
              <p className="text-muted-foreground">
                Hãy bắt đầu đặt cơm để xem thống kê!
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}