import { useState } from "react";
import { FoodItem, OrderItem } from "@/types/food";
import { useProducts } from "@/hooks/useProducts";
import { FoodItemCard } from "@/components/FoodItemCard";
import { OrderConfirmationDialog } from "@/components/OrderConfirmationDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { orderService } from "@/services/orderService";
import { useCustomerName } from "@/hooks/useCustomerName";
import { Package, Utensils, ShoppingCart, History, X, Trash2, Loader2, AlertCircle } from "lucide-react";

export function OrderPage() {
  const [selectedSideItems, setSelectedSideItems] = useState<Set<string>>(new Set());
  const [selectedMainItems, setSelectedMainItems] = useState<Set<string>>(new Set());
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  // const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);

  const { customerName } = useCustomerName();
  const { sideItems, mainItems, allItems, isLoading, error, refetch } = useProducts();

  const getSelectedItems = (): FoodItem[] => {
    const selectedItems: FoodItem[] = [];
    
    // Add selected side items
    selectedSideItems.forEach(id => {
      const item = sideItems.find(item => item.id === id);
      if (item) selectedItems.push(item);
    });
    
    // Add selected main items
    selectedMainItems.forEach(id => {
      const item = mainItems.find(item => item.id === id);
      if (item) selectedItems.push(item);
    });
    
    return selectedItems;
  };

  const selectedItems = getSelectedItems();

  const totalPrice = selectedItems.reduce((sum, item) => sum + item.price, 0);
  const hasSelection = selectedItems.length > 0;


  const handleItemSelection = (itemId: string, selected: boolean) => {
    const item = allItems.find(item => item.id === itemId);

    if (!item) return;

    if (item.type === 'side') {
      const newSelectedSides = new Set(selectedSideItems);
      if (selected) {
        newSelectedSides.add(itemId);
      } else {
        newSelectedSides.delete(itemId);
      }
      setSelectedSideItems(newSelectedSides);
    } else {
      const newSelectedMains = new Set(selectedMainItems);
      if (selected) {
        newSelectedMains.add(itemId);
      } else {
        newSelectedMains.delete(itemId);
      }
      setSelectedMainItems(newSelectedMains);
    }
  };

  const handleConfirmOrder = async (customerName: string, items: OrderItem[], total: number) => {
    // Reset form
    setSelectedSideItems(new Set());
    setSelectedMainItems(new Set());
  };

  const handleRemoveItem = async (itemId: string) => {
    const item = allItems.find(item => item.id === itemId);
    if (!item) return;

    // Remove from selected items
    if (item.type === 'side') {
      const newSelectedSides = new Set(selectedSideItems);
      newSelectedSides.delete(itemId);
      setSelectedSideItems(newSelectedSides);
    } else {
      const newSelectedMains = new Set(selectedMainItems);
      newSelectedMains.delete(itemId);
      setSelectedMainItems(newSelectedMains);
    }
  };

  const handleCancelOrder = async () => {
    // Clear all selected items
    setSelectedSideItems(new Set());
    setSelectedMainItems(new Set());

  };

  const formatPrice = (price: number) => `¥${price}`;

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            🍱 Đặt Cơm Hôm Nay
          </h1>
          <p className="text-muted-foreground">
            Chọn món ăn yêu thích và đặt cơm ngay!
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <Card className="bg-gradient-card shadow-card">
            <CardContent className="p-8">
              <div className="flex items-center justify-center space-x-3">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
                <span className="text-lg">Đang tải menu...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && (
          <Card className="bg-gradient-card shadow-card border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3 text-red-600">
                <AlertCircle className="h-6 w-6" />
                <div className="space-y-2">
                  <p className="font-medium">Không thể tải menu</p>
                  <p className="text-sm text-red-500">{error}</p>
                  <Button
                    onClick={refetch}
                    variant="outline"
                    size="sm"
                    className="mt-2"
                  >
                    Thử lại
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Side Items Section */}
        {!isLoading && !error && (
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="bg-food-secondary/20 p-2 rounded-lg">
                  <Package className="h-5 w-5 text-food-secondary" />
                </div>
                <span>📦 Món Phụ</span>
                <Badge variant="secondary" className="ml-auto">
                  Chọn nhiều món
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sideItems.map(item => (
                  <FoodItemCard
                    key={item.id}
                    item={item}
                    isSelected={selectedSideItems.has(item.id)}
                    onSelectionChange={handleItemSelection}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Items Section */}
        {!isLoading && !error && (
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="bg-primary/20 p-2 rounded-lg">
                  <Utensils className="h-5 w-5 text-primary" />
                </div>
                <span>🍱 Món Chính</span>
                <Badge variant="secondary" className="ml-auto">
                  Chọn nhiều món
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mainItems.map(item => (
                  <FoodItemCard
                    key={item.id}
                    item={item}
                    isSelected={selectedMainItems.has(item.id)}
                    onSelectionChange={handleItemSelection}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Order Summary & Action */}
        {/* {hasSelection && ( */}
          <Card className="bg-gradient-card shadow-elegant animate-fade-in">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header with History Icon */}
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-primary" />
                    Đơn hàng của bạn
                  </h3>
                  {/* <Button
                    onClick={() => setIsHistoryDialogOpen(true)}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <History className="h-4 w-4" />
                    Lịch sử
                  </Button> */}
                </div>

                {/* Selected Items List */}
                <div className="space-y-2">
                  <p className="text-muted-foreground text-sm">
                    {selectedItems.length} món đã chọn
                  </p>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {selectedItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between bg-muted/50 rounded-lg p-2"
                      >
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {item.name}
                          </Badge>
                          <span className="text-sm font-medium">
                            {formatPrice(item.price)}
                          </span>
                        </div>
                        <Button
                          onClick={() => handleRemoveItem(item.id)}
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-100"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Total and Actions */}
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 pt-2 border-t">
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-primary">
                      Tổng: {formatPrice(totalPrice)}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                    disabled={!hasSelection}
                      onClick={handleCancelOrder}
                      variant="outline"
                      className="gap-2 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      Cancel
                    </Button>
                    <Button
                      disabled={!hasSelection}
                      onClick={() => setIsDialogOpen(true)}
                      className="bg-gradient-primary hover:opacity-90 px-6 py-2 font-medium"
                      size="lg"
                    >
                      Order
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        {/* )} */}

        {/* Confirmation Dialog */}
        <OrderConfirmationDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          selectedItems={selectedItems}
          onConfirmOrder={handleConfirmOrder}
        />

        {/* History Dialog */}
        {/* <OrderHistoryDialog
          isOpen={isHistoryDialogOpen}
          onClose={() => setIsHistoryDialogOpen(false)}
          userName={customerName}
        /> */}
      </div>
    </div>
  );
}
