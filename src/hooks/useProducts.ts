import { useState, useEffect } from "react";
import { FoodItem } from "@/types/food";
import { productService } from "@/services/productService";

export interface UseProductsState {
  sideItems: FoodItem[];
  mainItems: FoodItem[];
  allItems: FoodItem[];
  isLoading: boolean;
  error?: string;
  refetch: () => Promise<void>;
}

/**
 * Custom hook to fetch and manage product data from the database
 */
export function useProducts(): UseProductsState {
  const [state, setState] = useState<{
    sideItems: FoodItem[];
    mainItems: FoodItem[];
    isLoading: boolean;
    error?: string;
  }>({
    sideItems: [],
    mainItems: [],
    isLoading: true,
    error: undefined,
  });

  const fetchProducts = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: undefined }));

      // Fetch all products from database
      const result = await productService.getAllProducts();

      if (!result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || "Failed to fetch products",
        }));
        return;
      }

      const products = result.data || [];

      // Separate products by category
      const sideItems = products.filter(item => item.type === "side");
      const mainItems = products.filter(item => item.type === "main");

      setState({
        sideItems,
        mainItems,
        isLoading: false,
        error: undefined,
      });

      console.log(`✅ Products loaded: ${sideItems.length} side items, ${mainItems.length} main items`);
    } catch (error) {
      console.error("❌ Error in useProducts:", error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: `Unexpected error: ${error}`,
      }));
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    sideItems: state.sideItems,
    mainItems: state.mainItems,
    allItems: [...state.sideItems, ...state.mainItems],
    isLoading: state.isLoading,
    error: state.error,
    refetch: fetchProducts,
  };
}
