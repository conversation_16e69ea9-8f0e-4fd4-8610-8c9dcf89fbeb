import { useState, useEffect, useCallback } from "react";

const CUSTOMER_NAME_KEY = "food-order-customer-name";

export interface UseCustomerNameReturn {
  customerName: string;
  setCustomerName: (name: string) => void;
  saveCustomerName: (name: string) => void;
  clearCustomerName: () => void;
  isLoaded: boolean;
}

/**
 * Custom hook for managing customer name persistence in localStorage
 * Provides auto-fill functionality and graceful error handling
 */
export function useCustomerName(): UseCustomerNameReturn {
  const [customerName, setCustomerNameState] = useState<string>("");
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  /**
   * Safely get value from localStorage
   */
  const getStoredCustomerName = useCallback((): string => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return "";
      }
      
      const stored = localStorage.getItem(CUSTOMER_NAME_KEY);
      return stored && stored !== "null" && stored !== "undefined" ? stored : "";
    } catch (error) {
      console.warn("Failed to read customer name from localStorage:", error);
      return "";
    }
  }, []);

  /**
   * Safely set value in localStorage
   */
  const setStoredCustomerName = useCallback((name: string): void => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return;
      }
      
      if (name && name.trim()) {
        localStorage.setItem(CUSTOMER_NAME_KEY, name.trim());
      } else {
        localStorage.removeItem(CUSTOMER_NAME_KEY);
      }
    } catch (error) {
      console.warn("Failed to save customer name to localStorage:", error);
    }
  }, []);

  /**
   * Load customer name from localStorage on mount
   */
  useEffect(() => {
    const storedName = getStoredCustomerName();
    setCustomerNameState(storedName);
    setIsLoaded(true);
  }, [getStoredCustomerName]);

  /**
   * Update customer name in state (for form input)
   */
  const setCustomerName = useCallback((name: string) => {
    setCustomerNameState(name);
  }, []);

  /**
   * Save customer name to localStorage (called after successful order)
   */
  const saveCustomerName = useCallback((name: string) => {
    const trimmedName = name.trim();
    if (trimmedName) {
      setStoredCustomerName(trimmedName);
      setCustomerNameState(trimmedName);
    }
  }, [setStoredCustomerName]);

  /**
   * Clear customer name from both state and localStorage
   */
  const clearCustomerName = useCallback(() => {
    setCustomerNameState("");
    setStoredCustomerName("");
  }, [setStoredCustomerName]);

  return {
    customerName,
    setCustomerName,
    saveCustomerName,
    clearCustomerName,
    isLoaded,
  };
}
