import { useEffect, useState } from "react";
import { productSyncService } from "@/services/productSyncService";

export interface SupabaseInitState {
  isInitialized: boolean;
  isLoading: boolean;
  error?: string;
  syncedProductsCount?: number;
}

/**
 * Hook to initialize Supabase data on app startup
 * This ensures products are synced and the database is ready for use
 */
export function useSupabaseInit(): SupabaseInitState {
  const [state, setState] = useState<SupabaseInitState>({
    isInitialized: false,
    isLoading: true,
  });

  useEffect(() => {
    let isMounted = true;

    const initializeSupabase = async () => {
      try {
        console.log("🚀 Starting Supabase initialization...");
        setState(prev => ({ ...prev, isLoading: true, error: undefined }));

        // Sync products with database
        const syncResult = await productSyncService.syncAllProducts();

        if (!isMounted) return;

        if (syncResult.success) {
          console.log("✅ Supabase initialization successful!", {
            syncedProductsCount: syncResult.syncedCount
          });
          setState({
            isInitialized: true,
            isLoading: false,
            syncedProductsCount: syncResult.syncedCount,
          });
        } else {
          console.error("❌ Supabase initialization failed:", syncResult.error);
          setState({
            isInitialized: false,
            isLoading: false,
            error: syncResult.error || "Failed to initialize Supabase",
          });
        }
      } catch (error) {
        if (!isMounted) return;

        console.error("❌ Unexpected error during Supabase initialization:", error);
        setState({
          isInitialized: false,
          isLoading: false,
          error: `Initialization error: ${error}`,
        });
      }
    };

    initializeSupabase();

    return () => {
      isMounted = false;
    };
  }, []);

  return state;
}
