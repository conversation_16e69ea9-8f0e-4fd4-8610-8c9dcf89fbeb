import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Database,
  Server,
  Globe,
  Code,
  Layers,
  Package,
  Shield,
  Zap,
  Users,
  ShoppingCart,
  BarChart3,
  History,
} from "lucide-react";

interface ProjectInfoModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProjectInfoModal({ open, onOpenChange }: ProjectInfoModalProps) {
  const techStack = [
    { name: "React 18", category: "Frontend" },
    { name: "TypeScript", category: "Language" },
    { name: "Vite", category: "Build Tool" },
    { name: "Tailwind CSS", category: "Styling" },
    { name: "shadcn/ui", category: "UI Library" },
    { name: "Supabase", category: "Backend" },
    { name: "PostgreSQL", category: "Database" },
    { name: "TanStack Query", category: "State Management" },
  ];

  const features = [
    { icon: ShoppingCart, name: "Order Management", desc: "Interactive menu with Japanese dishes" },
    { icon: BarChart3, name: "Analytics Dashboard", desc: "Revenue tracking and statistics" },
    { icon: History, name: "Order History", desc: "Complete audit trail of activities" },
    { icon: Users, name: "Customer Management", desc: "Track customer order patterns" },
  ];

  const dbTables = [
    { name: "users", desc: "Customer information", fields: "id, name, created_at, updated_at, deleted_at" },
    { name: "products", desc: "Menu items (Japanese dishes)", fields: "id, name, price, category, created_at, updated_at, deleted_at" },
    { name: "orders", desc: "Order records with payment status", fields: "id, user_id, total_price, is_paid, order_date, created_at, updated_at, deleted_at" },
    { name: "order_items", desc: "Individual items within orders", fields: "id, order_id, product_id, quantity, created_at, updated_at, deleted_at" },
    { name: "order_history", desc: "Complete audit trail", fields: "id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at" },
    { name: "debts", desc: "Customer debt tracking", fields: "id, user_id, amount, created_at, updated_at, deleted_at" },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Code className="h-6 w-6 text-primary" />
            Project Information
          </DialogTitle>
        </DialogHeader>
        
        <ScrollArea className="max-h-[calc(90vh-120px)] pr-4">
          <div className="space-y-6">
            {/* Project Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-blue-500" />
                  Project Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-lg mb-2">Japanese Restaurant Order Management Dashboard</h4>
                  <p className="text-muted-foreground leading-relaxed">
                    A comprehensive order management system for a Japanese restaurant, built with React, TypeScript, and Supabase. 
                    This application allows customers to place orders, track order history, and provides management with detailed 
                    analytics and order management capabilities.
                  </p>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {features.map((feature, index) => (
                    <div key={index} className="text-center p-3 rounded-lg bg-muted/50">
                      <feature.icon className="h-8 w-8 mx-auto mb-2 text-primary" />
                      <h5 className="font-medium text-sm">{feature.name}</h5>
                      <p className="text-xs text-muted-foreground mt-1">{feature.desc}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Architecture */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="h-5 w-5 text-green-500" />
                  Architecture
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Zap className="h-4 w-4 text-orange-500" />
                      Frontend
                    </h4>
                    <div className="space-y-2">
                      {techStack.filter(tech => ['Frontend', 'Language', 'Build Tool', 'Styling', 'UI Library', 'State Management'].includes(tech.category)).map((tech, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm">{tech.name}</span>
                          <Badge variant="outline" className="text-xs">{tech.category}</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Server className="h-4 w-4 text-blue-500" />
                      Backend
                    </h4>
                    <div className="space-y-2">
                      {techStack.filter(tech => ['Backend', 'Database'].includes(tech.category)).map((tech, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm">{tech.name}</span>
                          <Badge variant="outline" className="text-xs">{tech.category}</Badge>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-4 w-4 text-green-500" />
                        <span className="font-medium text-sm">Features</span>
                      </div>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• Row Level Security (RLS)</li>
                        <li>• Real-time subscriptions</li>
                        <li>• Automatic timestamps</li>
                        <li>• Soft delete support</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Database Schema */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-purple-500" />
                  Database Schema
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    PostgreSQL database with {dbTables.length} main tables, all with soft delete support and automatic timestamps.
                  </p>
                  
                  <div className="grid gap-3">
                    {dbTables.map((table, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-semibold text-sm flex items-center gap-2">
                            <Package className="h-4 w-4 text-blue-500" />
                            {table.name}
                          </h5>
                          <Badge variant="secondary" className="text-xs">Table</Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mb-2">{table.desc}</p>
                        <div className="text-xs font-mono bg-muted/50 p-2 rounded text-muted-foreground">
                          {table.fields}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h5 className="font-semibold mb-2">Key Relationships</h5>
                      <ul className="space-y-1 text-muted-foreground">
                        <li>• users (1) ↔ (N) orders</li>
                        <li>• users (1) ↔ (N) debts</li>
                        <li>• orders (1) ↔ (N) order_items</li>
                        <li>• products (1) ↔ (N) order_items</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-semibold mb-2">Menu Items</h5>
                      <ul className="space-y-1 text-muted-foreground">
                        <li>• 11 Main dishes (¥400-500)</li>
                        <li>• 10 Side dishes (¥30-100)</li>
                        <li>• Japanese names with descriptions</li>
                        <li>• Category-based organization</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
