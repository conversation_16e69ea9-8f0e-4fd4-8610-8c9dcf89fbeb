import { FoodItem } from "@/types/food";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface FoodItemCardProps {
  item: FoodItem;
  isSelected: boolean;
  onSelectionChange: (itemId: string, selected: boolean) => void;
  selectedMainItem?: string;
}

export function FoodItemCard({ 
  item, 
  isSelected, 
  onSelectionChange,
  selectedMainItem 
}: FoodItemCardProps) {
  const formatPrice = (price: number) => `¥${price}`;

  const handleCardClick = () => {
    onSelectionChange(item.id, !isSelected);
  };

  return (
    <Card 
      className={`bg-gradient-card shadow-card hover:shadow-food transition-all duration-300 cursor-pointer
        ${isSelected ? 'ring-2 ring-primary shadow-elegant' : 'hover:shadow-lg'}
      `}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        {item.type === 'side' ? (
          <div className="flex items-center space-x-3" id="out-box">
            <Checkbox
              id={item.id}
              checked={isSelected}
              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary pointer-events-none"
            />
            <Label 
              htmlFor={item.id} 
              className="flex-1 cursor-pointer font-medium text-foreground pointer-events-none"
            >
              <div className="flex justify-between items-center">
                <span>{item.name}</span>
                <span className="text-primary font-semibold">
                  {formatPrice(item.price)}
                </span>
              </div>
            </Label>
          </div>
        ) : (
          <div className="flex items-center space-x-3">
            <Checkbox
              id={item.id}
              checked={isSelected}
              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary pointer-events-none"
            />
            <Label 
              htmlFor={item.id} 
              className="flex-1 cursor-pointer font-medium text-foreground pointer-events-none"
            >
              <div className="flex justify-between items-center">
                <span>{item.name}</span>
                <span className="text-primary font-semibold">
                  {formatPrice(item.price)}
                </span>
              </div>
            </Label>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
