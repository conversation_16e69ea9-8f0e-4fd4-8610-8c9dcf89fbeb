import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useOrderHistory, useAllOrderHistory } from "@/hooks/useOrderHistory";
import {
  ShoppingCart,
  Trash2,
  XCircle,
  Clock,
  RefreshCw,
  AlertCircle,
  History,
  Users
} from "lucide-react";
import { OrderHistoryActionType } from "@/services/orderHistoryService";

interface OrderHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userName: string;
  showAllUsers?: boolean; // New prop to determine if showing all users or single user
}

const getActionIcon = (actionType: OrderHistoryActionType) => {
  switch (actionType) {
    case "order_placed":
      return <ShoppingCart className="h-4 w-4 text-green-600" />;
    case "item_removed":
      return <Trash2 className="h-4 w-4 text-orange-600" />;
    case "order_cancelled":
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4 text-gray-600" />;
  }
};

const getActionColor = (actionType: OrderHistoryActionType) => {
  switch (actionType) {
    case "order_placed":
      return "bg-green-100 text-green-800 border-green-200";
    case "item_removed":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "order_cancelled":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return {
    date: date.toLocaleDateString('vi-VN'),
    time: date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    }),
  };
};

export function OrderHistoryDialog({ isOpen, onClose, userName, showAllUsers = false }: OrderHistoryDialogProps) {
  // Use different hooks based on mode
  const singleUserHistory = useOrderHistory(userName);
  const allUsersHistory = useAllOrderHistory();

  // Determine which data to use based on mode
  const isShowingAllUsers = showAllUsers || !userName.trim();
  const { events, isLoading, error, refreshHistory, clearError } = isShowingAllUsers
    ? allUsersHistory
    : singleUserHistory;

  const handleRefresh = async () => {
    await refreshHistory();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isShowingAllUsers ? (
              <>
                <Users className="h-5 w-5 text-primary" />
                Lịch sử đặt món - Tất cả người dùng (Hôm nay)
              </>
            ) : (
              <>
                <History className="h-5 w-5 text-primary" />
                Lịch sử đặt món - {userName}
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {isShowingAllUsers
                ? "Hiển thị tất cả hoạt động đặt món hôm nay của mọi người dùng (sắp xếp theo thời gian)"
                : "Hiển thị tất cả hoạt động đặt món của bạn"
              }
            </p>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <div className="flex-1">
                  <p className="text-red-800 text-sm font-medium">Lỗi tải dữ liệu</p>
                  <p className="text-red-600 text-xs">{error}</p>
                </div>
                <Button
                  onClick={clearError}
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:text-red-800"
                >
                  Đóng
                </Button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center space-y-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                <p className="text-sm text-muted-foreground">Đang tải lịch sử...</p>
              </div>
            </div>
          )}

          {/* History Timeline */}
          {!isLoading && (
            <ScrollArea className="h-[400px] pr-4">
              {events.length === 0 ? (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Chưa có lịch sử đặt món</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Lịch sử sẽ được hiển thị khi bạn đặt món
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {events.map((event, index) => {
                    const { date, time } = formatDateTime(event.createdAt);
                    const isLast = index === events.length - 1;

                    return (
                      <div key={event.id} className="relative">
                        {/* Timeline Line */}
                        {!isLast && (
                          <div className="absolute left-6 top-12 w-0.5 h-8 bg-border"></div>
                        )}

                        {/* Event Card */}
                        <div className="flex gap-4">
                          {/* Icon */}
                          <div className="flex-shrink-0 w-12 h-12 rounded-full bg-background border-2 border-border flex items-center justify-center">
                            {getActionIcon(event.actionType)}
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <div className="bg-card border rounded-lg p-4 shadow-sm">
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium text-foreground">
                                    {event.description}
                                  </p>
                                  
                                  {/* Additional Details */}
                                  <div className="flex flex-wrap gap-2 mt-2">
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${getActionColor(event.actionType)}`}
                                    >
                                      {event.actionType === "order_placed" && "Đặt món"}
                                      {event.actionType === "item_removed" && "Xoá món"}
                                      {event.actionType === "order_cancelled" && "Huỷ đơn"}
                                    </Badge>

                                    {/* Show user name when in all-users mode */}
                                    {isShowingAllUsers && (
                                      <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                                        👤 {event.userName}
                                      </Badge>
                                    )}

                                    {event.itemName && (
                                      <Badge variant="secondary" className="text-xs">
                                        {event.itemName}
                                      </Badge>
                                    )}

                                    {event.totalAmount && (
                                      <Badge variant="outline" className="text-xs">
                                        ¥{event.totalAmount.toLocaleString()}
                                      </Badge>
                                    )}
                                  </div>
                                </div>

                                {/* Timestamp */}
                                <div className="text-right text-xs text-muted-foreground">
                                  <div>{time}</div>
                                  <div>{date}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          )}

          {/* Footer */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose} variant="outline">
              Đóng
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
