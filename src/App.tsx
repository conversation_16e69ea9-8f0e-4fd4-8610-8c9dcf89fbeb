import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useSupabaseInit } from "@/hooks/useSupabaseInit";
import { Layout } from "@/components/Layout";
import { OrderPage } from "@/pages/OrderPage";
import { DashboardPage } from "@/pages/DashboardPage";
import { OrderManagementPage } from "@/pages/OrderManagementPage";
import NotFound from "./pages/NotFound";


const queryClient = new QueryClient();

const AppContent = () => {
  const supabaseInit = useSupabaseInit();
  console.log("%c 🤩: AppContent -> supabaseInit ", "font-size:16px;background-color:#a640d9;color:white;", supabaseInit)

  // Show loading state while initializing
  if (supabaseInit.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-warm flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Initializing application...</p>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed, but allow app to continue
  if (supabaseInit.error) {
    console.warn("⚠️ Supabase initialization failed, continuing with localStorage only:", supabaseInit.error);
    // Don't block the app, just log the error and continue
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Navigate to="/orders" replace />} />
          <Route path="orders" element={<OrderPage />} />
          <Route path="order-manage" element={<OrderManagementPage />} />
          <Route path="statistics" element={<DashboardPage />} />
        </Route>
        {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AppContent />
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
