@tailwind base;
@tailwind components;
@tailwind utilities;

/* Food ordering app design system with warm, appetizing colors */

@layer base {
  :root {
    /* Main background - warm cream */
    --background: 30 20% 98%;
    --foreground: 20 14% 12%;

    /* Cards - pure white with slight warmth */
    --card: 0 0% 100%;
    --card-foreground: 20 14% 12%;

    /* Popovers */
    --popover: 0 0% 100%;
    --popover-foreground: 20 14% 12%;

    /* Primary - warm orange/red (food theme) */
    --primary: 15 85% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 25 90% 65%;

    /* Secondary - fresh green */
    --secondary: 120 25% 92%;
    --secondary-foreground: 140 30% 25%;

    /* Muted tones */
    --muted: 30 15% 94%;
    --muted-foreground: 20 8% 45%;

    /* Accent - warm yellow */
    --accent: 45 90% 75%;
    --accent-foreground: 20 14% 12%;

    /* Destructive */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs */
    --border: 20 20% 88%;
    --input: 20 20% 88%;
    --ring: 15 85% 55%;

    /* Food app specific colors */
    --food-primary: 15 85% 55%;
    --food-secondary: 140 50% 50%;
    --food-accent: 45 90% 65%;
    --food-warning: 35 85% 60%;
    --food-success: 140 70% 45%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--food-secondary)), hsl(140 40% 60%));
    --gradient-warm: linear-gradient(135deg, hsl(30 50% 95%), hsl(45 30% 98%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(30 20% 98%));

    /* Shadows */
    --shadow-food: 0 4px 20px -2px hsl(var(--primary) / 0.15);
    --shadow-card: 0 2px 15px -3px hsl(20 20% 20% / 0.1);
    --shadow-elegant: 0 8px 30px -8px hsl(var(--primary) / 0.2);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}