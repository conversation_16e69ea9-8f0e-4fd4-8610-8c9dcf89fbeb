# Japanese Restaurant Order Management Dashboard

A comprehensive order management system for a Japanese restaurant, built with React, TypeScript, and Supabase. This application allows customers to place orders, track order history, and provides management with detailed analytics and order management capabilities.

## 🍱 Project Overview

This is a full-stack web application designed for a Japanese restaurant that serves both main dishes (主菜) and side dishes (副菜). The system supports:

- **Customer Order Placement**: Interactive menu with Japanese dishes
- **Order Management**: Real-time order tracking and management
- **Analytics Dashboard**: Revenue tracking, order statistics, and popular items analysis
- **Order History**: Complete audit trail of all order activities
- **Debt Management**: Track paid/unpaid orders
- **Multi-language Support**: Japanese dish names with Vietnamese descriptions

## 🏗️ Architecture

### Frontend
- **React 18** with TypeScript for type safety
- **Vite** for fast development and building
- **React Router** for client-side routing
- **TanStack Query** for server state management
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Recharts** for data visualization

### Backend
- **Supabase** as Backend-as-a-Service
- **PostgreSQL** database with Row Level Security
- **Real-time subscriptions** for live updates

### Key Features
- Responsive design for mobile and desktop
- Real-time order updates
- Persistent data storage with localStorage fallback
- Comprehensive error handling
- Type-safe database operations

## 📊 Database Schema

The application uses a PostgreSQL database hosted on Supabase with the following tables:

### `users` Table
Stores customer information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique user identifier |
| `name` | TEXT | NOT NULL | Customer name |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

### `products` Table
Stores menu items (food products).

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique product identifier |
| `name` | TEXT | NOT NULL | Product name (Japanese) |
| `price` | DECIMAL(10,2) | NOT NULL | Product price in JPY |
| `category` | product_category | NOT NULL | Product category (main/side) |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

**Enum**: `product_category` - Values: `'main'`, `'side'`

### `orders` Table
Stores order information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique order identifier |
| `user_id` | UUID | NOT NULL, FOREIGN KEY → users(id) | Reference to customer |
| `total_price` | DECIMAL(10,2) | NOT NULL | Total order amount |
| `is_paid` | BOOLEAN | DEFAULT FALSE | Payment status |
| `order_date` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Order placement date |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

### `order_items` Table
Stores individual items within each order.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique order item identifier |
| `order_id` | UUID | NOT NULL, FOREIGN KEY → orders(id) | Reference to order |
| `product_id` | UUID | NOT NULL, FOREIGN KEY → products(id) | Reference to product |
| `quantity` | INTEGER | NOT NULL | Quantity ordered |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

### `order_history` Table
Tracks all order-related activities for audit purposes.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique history record identifier |
| `user_name` | TEXT | NOT NULL | Customer name (denormalized for performance) |
| `action_type` | TEXT | NOT NULL, CHECK IN ('order_placed', 'item_removed', 'order_cancelled') | Type of action performed |
| `order_id` | UUID | FOREIGN KEY → orders(id) ON DELETE SET NULL | Optional reference to order |
| `item_name` | TEXT | NULL | Item name for item-specific actions |
| `item_quantity` | INTEGER | NULL | Quantity for item actions |
| `total_amount` | DECIMAL(10,2) | NULL | Total amount for order actions |
| `description` | TEXT | NOT NULL | Human-readable action description |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Action timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

### `debts` Table
Tracks customer debt information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique debt record identifier |
| `user_id` | UUID | NOT NULL, FOREIGN KEY → users(id) | Reference to customer |
| `amount` | DECIMAL(10,2) | NOT NULL | Debt amount |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Last update timestamp |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | NULL | Soft delete timestamp |

### Database Relationships

```
users (1) ←→ (N) orders
users (1) ←→ (N) debts
orders (1) ←→ (N) order_items
products (1) ←→ (N) order_items
orders (1) ←→ (N) order_history (optional)
```

### Indexes

- `idx_order_history_user_name` - Efficient querying by customer name
- `idx_order_history_created_at` - Efficient querying by date (DESC)
- `idx_order_history_user_created` - Composite index for user + date queries

## 🍜 Menu Items

The application includes a predefined menu of Japanese dishes:

### Main Dishes (主菜) - ¥400-500
- **唐揚げ** (Karaage) - ¥400
- **のり** (Thập Cẩm - Nhỏ) - ¥400
- **そぼろ** (Soboro) - ¥400
- **ハンバーグ** (Hamburg) - ¥440
- **メンチカツ** (Menchi Katsu) - ¥440
- **生姜焼き** (Ginger Pork) - ¥440
- **とんかつ** (Tonkatsu) - ¥440
- **焼き肉** (Yakiniku) - ¥440
- **焼き魚** (Grilled Fish) - ¥440
- **カキフライ** (Fried Oyster) - ¥440
- **スポーツ** (Thập Cẩm - Lớn) - ¥500

### Side Dishes (副菜) - ¥30-100
- **キャベツ** (Cabbage) - ¥30
- **つけもの** (Pickles) - ¥40
- **ごはん（小）** (Small Rice) - ¥50
- **キムチ** (Kimchi) - ¥60
- **もやし炒め** (Stir-fried Bean Sprouts) - ¥70
- **サラダ** (Salad) - ¥80
- **だし巻き卵** (Rolled Egg) - ¥90
- **ポテト サラダ** (Potato Salad) - ¥100
- **みそ汁** (Miso Soup) - ¥100
- **唐揚げ パック** (Karaage Pack) - ¥100

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher) - [Install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- **npm** or **yarn** package manager
- **Supabase Account** - [Sign up at supabase.com](https://supabase.com)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/xcong4328/an-com-dashboard.git
   cd an-com-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Setup**

   Create a `.env` file in the root directory:
   ```env
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

   Get these values from your Supabase project dashboard:
   - Go to [Supabase Dashboard](https://app.supabase.com)
   - Select your project
   - Go to Settings → API
   - Copy the Project URL and anon/public key

4. **Database Setup**

   Run the migration to create the required tables:
   ```bash
   # If you have Supabase CLI installed
   supabase db push

   # Or manually run the SQL migration in your Supabase SQL editor
   # File: supabase/migrations/20241219_create_order_history.sql
   ```

5. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. **Open your browser**

   Navigate to `http://localhost:5173` to see the application.

### Build for Production

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory.

## 📱 Application Features

### 🛒 Order Management
- **Interactive Menu**: Browse main dishes and side dishes with prices
- **Order Placement**: Select items and quantities, confirm orders
- **Customer Names**: Associate orders with customer names
- **Order Confirmation**: Review order details before submission
- **Real-time Updates**: Live order status updates

### 📊 Analytics Dashboard
- **Daily Statistics**: Revenue, order count, payment status
- **Popular Items**: Most ordered dishes with quantities
- **Revenue Tracking**: Paid vs unpaid orders
- **Visual Charts**: Pie charts and bar graphs for data visualization
- **Debt Management**: Track outstanding payments

### 🗂️ Order History
- **Complete Audit Trail**: All order activities logged
- **Action Types**: Order placed, item removed, order cancelled
- **Customer-specific History**: Filter by customer name
- **Detailed Descriptions**: Human-readable action descriptions
- **Timestamp Tracking**: Precise timing of all activities

### 🎛️ Management Interface
- **Order List**: View all orders with status
- **Payment Tracking**: Mark orders as paid/unpaid
- **Order Details**: Detailed view of order items
- **Customer Management**: Track customer order patterns
- **Data Export**: Export order data for reporting

## 🛣️ Application Routes

The application uses React Router for navigation:

| Route | Component | Description |
|-------|-----------|-------------|
| `/` | Redirect to `/orders` | Root redirect |
| `/orders` | `OrderPage` | Main ordering interface |
| `/order-manage` | `OrderManagementPage` | Order management dashboard |
| `/statistics` | `DashboardPage` | Analytics and statistics |
| `*` | `NotFound` | 404 error page |

## 🔧 API Services

### Order Service (`orderService`)
Handles order operations with localStorage fallback:

```typescript
// Save order to database and localStorage
await orderService.saveOrder(customerName, orderItems, totalAmount);

// Get daily statistics
const stats = orderService.getDailyStats();

// Get all orders
const orders = orderService.getAllOrders();
```

### Supabase Order Service (`supabaseOrderService`)
Direct database operations:

```typescript
// Save order to Supabase
const result = await supabaseOrderService.saveOrderToDatabase(
  customerName,
  items,
  totalAmount
);

// Get order by ID
const order = await supabaseOrderService.getOrderById(orderId);

// Get all orders
const orders = await supabaseOrderService.getAllOrders();

// Get dish summary statistics
const summary = await supabaseOrderService.getDishSummary();
```

### Order History Service (`orderHistoryService`)
Manages order activity tracking:

```typescript
// Add order placed event
await orderHistoryService.addOrderPlacedEvent(customerName, totalAmount);

// Add item removed event
await orderHistoryService.addItemRemovedEvent(customerName, itemName, quantity);

// Add order cancelled event
await orderHistoryService.addOrderCancelledEvent(customerName);

// Get customer history
const history = await orderHistoryService.getCustomerHistory(customerName);
```

## 🎨 UI Components

The application uses shadcn/ui components with custom styling:

### Core Components
- `Header` - Navigation and branding
- `Layout` - Main application layout with routing
- `FoodItemCard` - Interactive menu item display
- `StatCard` - Statistics display cards
- `OrderConfirmationDialog` - Order review and confirmation
- `OrderHistoryDialog` - Customer order history display

### UI Library Components
- `Button`, `Card`, `Badge`, `Dialog` - Basic UI elements
- `Toaster`, `Sonner` - Notification systems
- `Tooltip`, `Popover` - Interactive overlays
- Charts from `recharts` for data visualization

## 🔒 Security Features

### Row Level Security (RLS)
- All database tables have RLS enabled
- Currently configured with permissive policies for development
- Can be restricted based on authentication requirements

### Data Validation
- TypeScript for compile-time type checking
- Zod schemas for runtime validation
- Form validation with React Hook Form

### Error Handling
- Graceful fallback to localStorage when Supabase is unavailable
- Comprehensive error logging and user feedback
- Network error recovery mechanisms

## 🧪 Development Tools

### Testing Utilities
The application includes several testing utilities in `src/utils/`:

- `addSampleOrders.ts` - Add sample data for testing
- `testDatabase.ts` - Database connection testing
- `quickTest.ts` - Quick functionality tests
- `testOrderHistory.ts` - Order history testing
- `testManagementHistory.ts` - Management features testing

### Development Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build for development (with source maps)
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview
```

## 📦 Dependencies

### Core Dependencies
- `react` & `react-dom` - React framework
- `typescript` - Type safety
- `vite` - Build tool and dev server
- `@supabase/supabase-js` - Supabase client
- `@tanstack/react-query` - Server state management
- `react-router-dom` - Client-side routing
- `react-hook-form` - Form management
- `zod` - Schema validation

### UI & Styling
- `tailwindcss` - Utility-first CSS framework
- `@radix-ui/*` - Headless UI components
- `lucide-react` - Icon library
- `recharts` - Chart library
- `class-variance-authority` - Component variants
- `tailwind-merge` - Tailwind class merging

### Development Tools
- `eslint` - Code linting
- `@types/*` - TypeScript definitions
- `autoprefixer` - CSS vendor prefixes
- `postcss` - CSS processing

## 🚀 Deployment

### Lovable Platform
The easiest way to deploy is through the Lovable platform:

1. Open [Lovable Project](https://lovable.dev/projects/5d0d2ad0-ee49-4efe-8095-482a352f41e7)
2. Click on Share → Publish
3. Your application will be deployed automatically

### Manual Deployment
For other platforms (Vercel, Netlify, etc.):

1. Build the project: `npm run build`
2. Deploy the `dist` directory
3. Set environment variables for Supabase
4. Configure redirects for SPA routing

### Environment Variables
Required for production:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and test thoroughly
4. Commit your changes: `git commit -m 'Add new feature'`
5. Push to the branch: `git push origin feature/new-feature`
6. Submit a pull request

## 📄 License

This project is part of the Lovable platform. Please refer to the Lovable terms of service for licensing information.

## 🆘 Support

- **Lovable Documentation**: [docs.lovable.dev](https://docs.lovable.dev)
- **Supabase Documentation**: [supabase.com/docs](https://supabase.com/docs)
- **React Documentation**: [react.dev](https://react.dev)

## 🔗 Links

- **Live Application**: [Deployed on Lovable](https://lovable.dev/projects/5d0d2ad0-ee49-4efe-8095-482a352f41e7)
- **Repository**: [GitHub](https://github.com/xcong4328/an-com-dashboard)
- **Supabase Project**: Project ID `yppmwrzhwsjgniholliy`

---

Built with ❤️ using React, TypeScript, and Supabase
