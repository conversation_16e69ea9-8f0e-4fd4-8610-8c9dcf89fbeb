-- Create order_history table for tracking all order-related activities
CREATE TABLE IF NOT EXISTS order_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_name TEXT NOT NULL, -- Store customer name directly for easy querying
    action_type TEXT NOT NULL CHECK (action_type IN ('order_placed', 'item_removed', 'order_cancelled')),
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL, -- Optional reference to order
    item_name TEXT, -- Name of the item for item_removed actions
    item_quantity INTEGER, -- Quantity for item actions
    total_amount DECIMAL(10,2), -- Total amount for order_placed actions
    description TEXT NOT NULL, -- Human-readable description of the action
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create index for efficient querying by user_name and created_at
CREATE INDEX IF NOT EXISTS idx_order_history_user_name ON order_history(user_name);
CREATE INDEX IF NOT EXISTS idx_order_history_created_at ON order_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_order_history_user_created ON order_history(user_name, created_at DESC);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_order_history_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_order_history_updated_at
    BEFORE UPDATE ON order_history
    FOR EACH ROW
    EXECUTE FUNCTION update_order_history_updated_at();

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE order_history ENABLE ROW LEVEL SECURITY;

-- Allow all operations for now (can be restricted later based on auth requirements)
CREATE POLICY "Allow all operations on order_history" ON order_history
    FOR ALL USING (true);

-- Insert some sample data for testing
INSERT INTO order_history (user_name, action_type, description) VALUES
('nmcong1', 'order_placed', 'nmcong1 đã đặt món lúc 12:00'),
('nmcong1', 'item_removed', 'nmcong1 đã xoá món 唐揚げ lúc 12:03'),
('nmcong1', 'order_cancelled', 'nmcong1 đã huỷ đặt món lúc 12:05');
